#!/bin/bash

echo "🚀 开始构建 Chrome MCP Server 扩展..."

# 确保在项目根目录
cd "$(dirname "$0")"

echo "📦 安装依赖..."
pnpm install

echo "🔧 构建共享包..."
pnpm run build:shared

echo "⚙️ 构建WASM包..."
pnpm run build:wasm

echo "🌐 构建Chrome扩展..."
pnpm run build:extension

echo "📁 进入扩展目录生成ZIP包..."
cd app/chrome-extension
pnpm run zip

echo "✅ 构建完成！"
echo ""
echo "📂 扩展文件位置:"
echo "   解压版本: app/chrome-extension/.output/chrome-mv3/"
echo "   ZIP包: app/chrome-extension/.output/chrome-mv3-*.zip"
echo ""
echo "🔗 加载到Chrome:"
echo "   1. 访问 chrome://extensions/"
echo "   2. 开启开发者模式"
echo "   3. 点击'加载已解压的扩展程序'"
echo "   4. 选择上述解压版本目录"
