# 本地开发服务使用指南 🔧

本文档专门介绍如何使用本地开发版本的 MCP Chrome Server，而不是通过 `npm install -g` 安装的全局版本。

## 📋 目录

- [概述](#概述)
- [环境准备](#环境准备)
- [快速启动](#快速启动)
- [配置说明](#配置说明)
- [验证连接](#验证连接)
- [开发调试](#开发调试)
- [常见问题](#常见问题)
- [与全局版本的区别](#与全局版本的区别)

## 🎯 概述

### 本地开发 vs 全局安装

| 方式 | 优势 | 使用场景 |
|------|------|----------|
| **本地开发** | 可修改代码、实时调试、版本控制 | 二次开发、功能定制、问题排查 |
| **全局安装** | 开箱即用、无需构建 | 普通使用、生产环境 |

## 🚀 环境准备

### 前置要求

- Node.js >= 18.19.0
- pnpm 或 npm
- Chrome/Chromium 浏览器
- Git（用于克隆项目）

### 克隆项目

```bash
# 克隆项目
git clone https://github.com/hangwin/mcp-chrome.git
cd mcp-chrome

# 安装依赖
pnpm install
```

## 🏗️ 快速启动

### 第一步：构建项目

```bash
# 构建所有必需组件（按顺序执行）
pnpm run build:shared    # 1. 构建共享包（必须先执行）
pnpm run build:native    # 2. 构建native-server
pnpm run build:extension # 3. 构建Chrome扩展（可选）
```

### 第二步：配置Native Messaging Host

**关键步骤：让Chrome扩展连接到本地服务**

```bash
# 进入native-server目录
cd app/native-server

# 创建用户级Native Messaging目录
mkdir -p ~/Library/Application\ Support/Google/Chrome/NativeMessagingHosts

# 创建指向本地版本的配置文件
cat > ~/Library/Application\ Support/Google/Chrome/NativeMessagingHosts/com.chromemcp.nativehost.json << EOF
{
  "name": "com.chromemcp.nativehost",
  "description": "Node.js Host for Browser Bridge Extension (Local Development)",
  "path": "$(pwd)/dist/run_host.sh",
  "type": "stdio",
  "allowed_origins": ["chrome-extension://hbdgbgagpkpjffpklnamcljpakneikee/"]
}
EOF

# 设置正确的文件权限
chmod 644 ~/Library/Application\ Support/Google/Chrome/NativeMessagingHosts/com.chromemcp.nativehost.json
chmod +x dist/run_host.sh
```

### 第三步：安装Chrome扩展

```bash
# 如果需要重新构建扩展
pnpm run build:extension
```

**在Chrome中加载扩展：**
1. 打开 `chrome://extensions/`
2. 启用"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择 `app/chrome-extension/dist/chrome-mv3/` 目录

### 第四步：启动和连接

1. **重启Chrome浏览器**（重要！让Native Messaging配置生效）

2. **点击Chrome扩展图标**，在弹出界面中：
   - 确认端口为 `12306`
   - 点击"⚡ 连接"按钮

3. **验证连接状态**：
   - 状态应显示为"服务已连接"
   - 可以看到服务启动时间

## ⚙️ 配置说明

### 端口配置

默认端口：`12306`

**修改端口：**
```bash
# 使用CLI命令修改（推荐）
cd app/native-server
node dist/cli.js update-port <新端口号>

# 示例：修改为8888
node dist/cli.js update-port 8888
```

**手动修改：**
```typescript
// 1. 修改Chrome扩展默认端口
// app/chrome-extension/common/constants.ts
export const NATIVE_HOST = {
  DEFAULT_PORT: 8888, // 修改这里
} as const;

// 2. 修改STDIO配置
// app/native-server/src/mcp/stdio-config.json
{
  "url": "http://127.0.0.1:8888/mcp"
}
```

### 开发模式启动

```bash
# 开发模式（自动重载）
pnpm dev:native

# 或者启动所有包的开发模式
pnpm dev
```

## ✅ 验证连接

### 检查服务状态

```bash
# 查看进程
ps aux | grep "node.*index.js"

# 查看端口占用
lsof -i :12306

# 查看连接日志
ls -la app/native-server/dist/logs/
tail -f app/native-server/dist/logs/native_host_wrapper_macos_*.log
```

### 测试MCP协议

```bash
# 测试MCP端点（服务连接后）
curl -X POST http://127.0.0.1:12306/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize", 
    "params": {
      "protocolVersion": "2024-11-05",
      "capabilities": {"tools": {}},
      "clientInfo": {"name": "test-client", "version": "1.0.0"}
    }
  }'
```

### 在MCP客户端中使用

```json
{
  "mcpServers": {
    "chrome-mcp-local": {
      "type": "streamableHttp",
      "url": "http://127.0.0.1:12306/mcp"
    }
  }
}
```

## 🔍 开发调试

### 实时日志查看

```bash
# 查看Native Messaging日志
tail -f app/native-server/dist/logs/native_host_wrapper_macos_*.log

# 查看错误日志  
tail -f app/native-server/dist/logs/native_host_stderr_macos_*.log
```

### 常用调试命令

```bash
# 停止所有相关进程
pkill -f "node.*index.js"

# 重新构建并启动
pnpm run build:shared && pnpm run build:native

# 查看Native Messaging配置
cat ~/Library/Application\ Support/Google/Chrome/NativeMessagingHosts/com.chromemcp.nativehost.json
```

### 开发工作流

```bash
# 1. 修改代码后重新构建
pnpm run build:shared  # 如果修改了shared包
pnpm run build:native  # 如果修改了native-server

# 2. 重启Chrome扩展连接
# 在Chrome扩展中点击断开再重新连接

# 3. 查看日志验证更改
tail -f app/native-server/dist/logs/*.log
```

## ❗ 常见问题

### 扩展显示"服务未连接"

**解决步骤：**
1. 确认Native Messaging配置正确：
   ```bash
   cat ~/Library/Application\ Support/Google/Chrome/NativeMessagingHosts/com.chromemcp.nativehost.json
   ```

2. 确认路径存在且可执行：
   ```bash
   ls -la app/native-server/dist/run_host.sh
   chmod +x app/native-server/dist/run_host.sh
   ```

3. 重启Chrome浏览器

4. 查看连接日志：
   ```bash
   ls -la app/native-server/dist/logs/
   ```

### 端口冲突

```bash
# 查看端口占用
lsof -i :12306

# 杀死占用进程
kill -9 $(lsof -ti :12306)

# 或修改端口
cd app/native-server
node dist/cli.js update-port 8888
```

### 构建失败

```bash
# 清理构建产物
pnpm run clean

# 删除node_modules重新安装
rm -rf node_modules app/*/node_modules packages/*/node_modules
pnpm install

# 按顺序重新构建
pnpm run build:shared
pnpm run build:native
```

### 权限问题（macOS）

```bash
# 确保文件权限正确
chmod +x app/native-server/dist/run_host.sh
chmod +x app/native-server/dist/index.js

# 如果遇到安全限制，在系统偏好设置中允许
# 系统偏好设置 > 安全性与隐私 > 通用
```

## 🆚 与全局版本的区别

### 文件位置对比

| 组件 | 全局安装版本 | 本地开发版本 |
|------|-------------|-------------|
| **Native Host配置** | 指向全局npm包 | 指向本地项目目录 |
| **启动脚本** | `~/.npm/global/.../run_host.sh` | `项目目录/app/native-server/dist/run_host.sh` |
| **日志位置** | 全局包目录 | `项目目录/app/native-server/dist/logs/` |
| **配置文件** | 全局包版本 | 本地可修改版本 |

### 开发优势

- ✅ **实时修改**：修改代码后重新构建即可生效
- ✅ **完整日志**：所有日志在项目目录下，便于调试
- ✅ **版本控制**：可以使用Git管理代码变更
- ✅ **自定义功能**：可以添加自定义工具和功能
- ✅ **问题排查**：可以直接查看和修改源码

### 切换回全局版本

如果需要切换回全局版本：

```bash
# 删除本地Native Messaging配置
rm ~/Library/Application\ Support/Google/Chrome/NativeMessagingHosts/com.chromemcp.nativehost.json

# 重新注册全局版本
npm install -g mcp-chrome-bridge
mcp-chrome-bridge register

# 重启Chrome浏览器
```

## 📝 总结

使用本地开发版本可以让你：

1. **完全控制代码**：可以修改任何功能
2. **实时调试**：详细的日志和错误信息
3. **功能定制**：添加自己需要的工具和功能
4. **问题排查**：直接访问源码进行问题定位

这种方式特别适合需要二次开发或深度定制MCP Chrome Server功能的开发者。

---

**需要帮助？** 查看项目的其他文档：
- [架构设计](ARCHITECTURE_zh.md)
- [工具列表](TOOLS_zh.md) 
- [贡献指南](CONTRIBUTING_zh.md)
- [故障排除](TROUBLESHOOTING_zh.md)
